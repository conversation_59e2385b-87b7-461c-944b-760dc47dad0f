import requests
import mysql.connector
from mysql.connector import Error
import time

base_url = 'https://www.hipermaxi.com'

def get_collections():
    return [
        {"id": 1, "name": "Abarrotes"}, 
        {"id": 2, "name": "<PERSON><PERSON><PERSON>"},
        {"id": 3, "name": "Carnes"},
        {"id": 8, "name": "Congelados"},
        {"id": 4, "name": "Fiambres"},
        {"id": 7, "name": "Lácteos y Derivados"},
        {"id": 5, "name": "Panadería"},
        {"id": 6, "name": "Pastelería y Masas Típicas"},
        {"id": 11, "name": "Cuidado del Bebé"},
        {"id": 9, "name": "Cuidado del Hogar"},
        {"id": 10, "name": "Cuidado Personal"},
        {"id": 34, "name": "Bazar Importación"},
        {"id": 13, "name": "<PERSON><PERSON>"},
        {"id": 36, "name": "Juguetería Importación"},
        {"id": 21, "name": "Juguetería"},
        {"id": 15, "name": "Farmacia Éticos"},
        {"id": 16, "name": "Farmacia Genéricos"},
        {"id": 14, "name": "Farmacia Otc"},
        {"id": 12, "name": "Frutas y Verduras"},
        {"id": 33, "name": "Granos y Hortalizas"},
    ]

def scrape_and_save(collection):
    """
    Scrapes product data from JSON API and saves it to a MySQL database.
    """
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qazwsx',
        'database': 'carlos'
    }
    
    conn = None
    cursor = None

    try:
        # Database Connection
        print("Connecting to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        print("Database connection successful.")

        # Create Table (if it doesn't exist)
        table_name = 'products'
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store VARCHAR(255) NOT NULL,
            category VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description VARCHAR(1000),
            price DECIMAL(10, 2) NOT NULL,
            price_discount DECIMAL(10, 2),
            product_url VARCHAR(2083),
            image_url VARCHAR(2083),
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_product (store, category, name)
        );
        """
        cursor.execute(create_table_query)
        print(f"Table '{table_name}' created or already exists.")

        # JSON API Scraping with Pagination
        category = collection['name']
        page_number = 1
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        total_products_scraped = 0

        while True:
            current_url = f'https://hipermaxi.com/tienda-api/api/v1/public/productos?IdMarket=67&IdLocatario=67&IdCategoria={collection["id"]}&Pagina={page_number}&Cantidad=50'
            print(f"\n--- Scraping Page {page_number}: {current_url} ---")
            
            response = requests.get(current_url, headers=headers)
            response.raise_for_status()

            # Parse JSON response
            data = response.json()
            products = data.get('productos', []) if 'productos' in data else data.get('data', [])

            if not products:
                print("No products found on this page. Ending scrape.")
                break

            print(f"Found {len(products)} products on this page. Extracting data...")
            product_data = []
            
            for product in products:
                try:
                    name = product.get('nombre', 'N/A')
                    description = product.get('descripcion', '')
                    
                    # Handle price - adjust field names based on actual JSON structure
                    price = float(product.get('precio', 0)) if product.get('precio') else 0.0
                    price_discount = float(product.get('precioDescuento', 0)) if product.get('precioDescuento') else None
                    
                    # Handle URLs
                    product_url = product.get('url', '')
                    if product_url and not product_url.startswith('http'):
                        product_url = base_url + product_url
                    
                    image_url = product.get('imagen', '') or product.get('imagenUrl', '')
                    if image_url and image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    elif image_url and not image_url.startswith('http'):
                        image_url = base_url + image_url

                    product_data.append(('Hipermaxi', category, name, description, price, price_discount, product_url, image_url))
                    total_products_scraped += 1
                    
                except Exception as e:
                    print(f"Error extracting data for a product: {e}")

            # Insert Data for the current page
            if product_data:
                insert_query = f"INSERT INTO {table_name} (store, category, name, description, price, price_discount, product_url, image_url) VALUES (%s, %s, %s, %s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE price = VALUES(price), price_discount = VALUES(price_discount), image_url = VALUES(image_url)"
                cursor.executemany(insert_query, product_data)
                conn.commit()
                print(f"{cursor.rowcount} records inserted/updated for this page.")
            
            page_number += 1
            time.sleep(1)  # Be respectful with API requests

        print(f"\n--- Scraping Complete ---")
        print(f"Total products scraped: {total_products_scraped}")

    except requests.exceptions.RequestException as e:
        print(f"Error fetching the URL: {e}")
    except Error as e:
        print(f"Error connecting to MySQL: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if conn and conn.is_connected():
            if cursor:
                cursor.close()
            conn.close()
            print("MySQL connection is closed.")

if __name__ == '__main__':
    collections = get_collections()
    for collection in collections:
        scrape_and_save(collection)

