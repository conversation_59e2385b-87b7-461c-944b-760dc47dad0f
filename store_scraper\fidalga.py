import requests
from bs4 import BeautifulSoup
import mysql.connector
from mysql.connector import Error
import time

def get_collections():
    current_url = 'https://www.fidalga.com/collections/jugos'
    base_url = 'https://www.fidalga.com'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    response = requests.get(current_url, headers=headers)
    response.raise_for_status()

    soup = BeautifulSoup(response.content, 'html.parser')
    collections = soup.find_all('li', class_='sidebar-link-lv1')
    collection_links = [base_url + link.find('a')['href'] for link in collections]
    return collection_links

def scrape_and_save(current_url):
    """
    Scrapes product data from all pages of a collection and saves it to a MySQL database.
    """
    # --- IMPORTANT: Replace with your MySQL database credentials ---
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'qazwsx',
        'database': 'carlos'
    }
    
    conn = None
    cursor = None

    try:
        # --- 1. Database Connection ---
        print("Connecting to MySQL database...")
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        print("Database connection successful.")

        # --- 2. Create Table (if it doesn't exist) ---
        table_name = 'products'
        create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            store VARCHAR(255) NOT NULL,
            category VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description VARCHAR(1000),
            price DECIMAL(10, 2) NOT NULL,
            price_discount DECIMAL(10, 2),
            product_url VARCHAR(2083),
            image_url VARCHAR(2083),
            scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_product (store, category, name)
        );
        """
        cursor.execute(create_table_query)
        print(f"Table '{table_name}' created or already exists.")

        # --- 3. Web Scraping with Pagination ---
        base_url = 'https://www.fidalga.com'
        # Start with the first page of the collection
        # current_url = 'https://www.fidalga.com/collections/jugos'
        category = current_url.split('/')[-1]
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        page_number = 1
        total_products_scraped = 0

        while current_url:
            print(f"\n--- Scraping Page {page_number}: {current_url} ---")
            
            response = requests.get(current_url, headers=headers)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # --- IMPORTANT: You need to inspect the website's HTML to find the correct selectors ---
            main_content = soup.find('div', id='CollectionProductGrid')
            products = main_content.find_all('div', class_='product-item')

            if not products:
                print("No products found on this page. Ending scrape.")
                break

            print(f"Found {len(products)} products on this page. Extracting data...")
            product_data = []
            for product in products:
                try:
                    name_element = product.find('a', class_='product-title')
                    price_element = product.select_one('.price-regular > span')
                    price_discount_element = product.select_one('.special-price')
                    if price_discount_element:
                        price_element = product.select_one('.old-price')
                        
                    image_element = product.select_one('.product-image img')
                    
                    name = name_element.text.strip() if name_element else 'N/A'

                    price_text = price_element.text.strip() if price_element else '0'
                    price = float(''.join(filter(str.isdigit, price_text))) / 100 if any(char.isdigit() for char in price_text) else 0.0
                    price_discount = None
                    if price_discount_element:
                        price_discount_text = price_discount_element.text.strip() if price_discount_element else '0'
                        price_discount = float(''.join(filter(str.isdigit, price_discount_text))) / 100 if any(char.isdigit() for char in price_discount_text) else 0.0

                    product_url = base_url + name_element.get('href') if name_element else None
                    image_url = image_element['data-srcset'] if image_element and 'data-srcset' in image_element.attrs else 'N/A'
                    image_url = image_url.split(',')[0].replace(' 360w', '')

                    if image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    elif not image_url.startswith('http'):
                        image_url = base_url + image_url

                    product_data.append(('Fidalga', category, name, price, price_discount, product_url, image_url))
                    total_products_scraped += 1
                except Exception as e:
                    print(f"Error extracting data for a product: {e}")

            # --- 4. Insert Data for the current page ---
            if product_data:
                insert_query = f"INSERT INTO {table_name} (store, category, name, price, price_discount, product_url, image_url) VALUES (%s, %s, %s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE price = VALUES(price), price_discount = VALUES(price_discount), image_url = VALUES(image_url)"
                cursor.executemany(insert_query, product_data)
                conn.commit()
                print(f"{cursor.rowcount} records inserted/updated for this page.")
            
            # --- 5. Find the next page URL ---
            # Inspect the "Next" or "Siguiente" button to find the correct tag and class/id
            next_page_link = soup.select_one('.infinite-scrolling.hide a') # Example selector
            
            if next_page_link and next_page_link.get('data-href'):
                # Construct the full URL for the next page
                next_page_href = next_page_link.get('data-href')
                if next_page_href.startswith('/'):
                    current_url = base_url + next_page_href
                else:
                    current_url = next_page_href
                page_number += 1
                time.sleep(1) # Be respectful and add a small delay between requests
            else:
                print("\nNo 'next page' link found. Reached the last page.")
                current_url = None # End the loop

        print(f"\n--- Scraping Complete ---")
        print(f"Total products scraped: {total_products_scraped}")


    except requests.exceptions.RequestException as e:
        print(f"Error fetching the URL: {e}")
    except Error as e:
        print(f"Error connecting to MySQL: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if conn and conn.is_connected():
            if cursor:
                cursor.close()
            conn.close()
            print("MySQL connection is closed.")

if __name__ == '__main__':
    collections = get_collections()
    for collection in collections:
        scrape_and_save(collection)
    # scrape_and_save('https://www.fidalga.com/collections/jugos')
